"use client";
import Default<PERSON><PERSON>on from "@/components/global/buttons/button";
import {
  Box,
  Field,
  Flex,
  HStack,
  Input,
  Stack,
  Table,
  Text,
  useFilter,
  useListCollection,
  VStack,
} from "@chakra-ui/react";
import { useState } from "react";
import { LuSearch, LuPlus, LuTrash2, <PERSON>Pencil } from "react-icons/lu";
import { useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import { useGetAllUsers } from "@/hook/users/useGetAllUsers";
import { formatInTimeZone } from "date-fns-tz";
import { translateUsersRole } from "@/utils/formatters/formatUsersRole";
import BasicModal from "@/components/global/modal/basic-modal";
import { useMutation } from "@tanstack/react-query";
import { toaster } from "@/components/ui/toaster";
import { api } from "@/services/api";
import { queryClient } from "@/services/queryClient";
import { GetUserDto } from "@/utils/types/DTO/users/users.dto";
import FormCombobox, {
  ComboboxOption,
} from "@/components/global/combobox/form-combobox";
import { useGetAllCategories } from "@/hook/hierarchy/useGetCategories";
import { useGetAllPermissions } from "@/hook/permissions/useGetAllPermissions";

const NewUserSchema = yup.object().shape({
  name: yup
    .string()
    .max(50, "O nome deve ter no máximo 50 caracteres")
    .required("O nome do usuário é obrigatório"),
  email: yup
    .string()
    .email("E-mail inválido")
    .required("O e-mail é obrigatório"),
  password: yup
    .string()
    .min(6, "A senha deve ter no mínimo 6 caracteres")
    .required("A senha é obrigatória"),
  confirmPassword: yup
    .string()
    .oneOf([yup.ref("password")], "As senhas devem ser iguais")
    .required("A confirmação de senha é obrigatória"),
  role: yup
    .string()
    .oneOf(["admin", "user"])
    .required("O papel do usuário é obrigatório"),

  managementId: yup.string().required("A gerência é obrigatória"),
  positionId: yup.string().required("O cargo é obrigatório"),
  permissionsIds: yup
    .array()
    .of(yup.string().required())
    .required("As permissões são obrigatórias"),
});

type NewUserFormData = yup.InferType<typeof NewUserSchema>;

const usersRoles = [
  { label: "Administrador", value: "admin" },
  { label: "Usuário", value: "user" },
];

export default function Users() {
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<GetUserDto | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const { data: usersData } = useGetAllUsers(searchTerm);

  const { contains } = useFilter({ sensitivity: "base" });

  const { collection, filter } = useListCollection({
    initialItems: usersRoles,
    filter: contains,
  });

  const { data: categoriesData } = useGetAllCategories(searchTerm);
  const { data: permissionsData } = useGetAllPermissions();

  const getManagementOptions = (): ComboboxOption[] => {
    return (
      categoriesData?.data
        ?.filter((cat) => cat.type === "MANAGEMENT")
        .map((cat) => ({
          label: cat.name,
          value: cat.secureId,
        })) || []
    );
  };

  const getPositionOptions = (): ComboboxOption[] => {
    return (
      categoriesData?.data
        ?.filter((cat) => cat.type === "POSITION")
        .map((cat) => ({
          label: cat.name,
          value: cat.secureId,
        })) || []
    );
  };

  const getPermissionsOptions = (): ComboboxOption[] => {
    return (
      permissionsData?.data?.map((perm) => ({
        label: perm.slug,
        value: perm.secureId,
      })) || []
    );
  };

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<NewUserFormData>({
    resolver: yupResolver(NewUserSchema),
    defaultValues: {
      name: "",
      email: "",
      role: undefined,
    },
  });

  const watchedType = watch("role");

  const addUser = useMutation({
    mutationFn: async (data: NewUserFormData) => {
      await api.post("/management/user", data);
    },
    onSuccess: () => {
      toaster.success({
        title: "Usuário adicionado com sucesso!",
      });
    },
  });

  const handleAddUser = async (data: NewUserFormData) => {
    try {
      await addUser.mutateAsync(data);
      queryClient.invalidateQueries({ queryKey: ["users"] });
      setIsAddModalOpen(false);
      reset();
    } catch (e) {
      console.log("Erro ao adicionar usuário", e);
    }
  };

  const handleOpenAddModal = () => {
    reset();
    setIsAddModalOpen(true);
  };

  const handleOpenEditModal = (user: GetUserDto) => {
    setSelectedUser(user);
    setValue("name", user.name);
    setValue("email", user.email);
    // setValue("role", user.role);
    setIsEditModalOpen(true);
  };

  const handleOpenDeleteModal = (user: GetUserDto) => {
    setSelectedUser(user);
    setIsDeleteModalOpen(true);
  };

  return (
    <Flex flex={1} position={"relative"} overflow={"hidden"} p={4}>
      <Stack w="100%" gap={6}>
        {/* Header */}
        <Text fontSize="2xl" fontWeight="bold" color="white">
          Usuários
        </Text>

        {/* Search and Filters */}
        <HStack gap={4} justify="space-between">
          <Box position="relative" flex={1} maxW="400px">
            <Input
              placeholder="Buscar..."
              bg="gray.800"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Box
              position="absolute"
              right={3}
              top="50%"
              transform="translateY(-50%)"
              color="gray.400"
            >
              <LuSearch />
            </Box>
          </Box>
          <DefaultButton onClick={handleOpenAddModal} size="md">
            <LuPlus />
            Adicionar Usuário
          </DefaultButton>
        </HStack>

        {/* Table */}
        <Box
          bg="gray.800"
          borderRadius="lg"
          border="1px solid"
          borderColor="gray.600"
          overflow="hidden"
        >
          <Table.Root size="md" variant="outline">
            <Table.Header bg="gray.700">
              <Table.Row>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Nome
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Tipo
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  Data de Criação
                </Table.ColumnHeader>
                <Table.ColumnHeader color="white" fontWeight="bold">
                  <Flex justify="center">Ações</Flex>
                </Table.ColumnHeader>
              </Table.Row>
            </Table.Header>
            <Table.Body>
              {usersData?.data?.map((item) => (
                <Table.Row key={item.secureId} _hover={{ bg: "gray.700" }}>
                  <Table.Cell>
                    <Text color="gray.300" fontWeight="medium">
                      {item.name}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {translateUsersRole(item.role)}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <Text color="gray.300" maxW="200px">
                      {formatInTimeZone(
                        item.createdAt,
                        "America/Sao_Paulo",
                        "dd/MM/yyyy HH:mm:ss"
                      )}
                    </Text>
                  </Table.Cell>
                  <Table.Cell>
                    <HStack gap={2} justifyContent={"center"}>
                      <DefaultButton
                        tooltipContent="Editar"
                        buttonColor="#156082"
                        size="sm"
                        onClick={() => handleOpenEditModal(item)}
                      >
                        <LuPencil />
                      </DefaultButton>
                      <DefaultButton
                        tooltipContent="Excluir"
                        buttonColor="red.500"
                        size="sm"
                        onClick={() => handleOpenDeleteModal(item)}
                      >
                        <LuTrash2 />
                      </DefaultButton>
                    </HStack>
                  </Table.Cell>
                </Table.Row>
              )) || (
                <Table.Row>
                  <Table.Cell colSpan={4}>
                    <Text color="gray.400" textAlign="center" py={4}>
                      Nenhuma categoria encontrada
                    </Text>
                  </Table.Cell>
                </Table.Row>
              )}
            </Table.Body>
          </Table.Root>
        </Box>
      </Stack>

      {/* Add User Modal */}
      <BasicModal
        open={isAddModalOpen}
        setOpen={setIsAddModalOpen}
        title="Adicionar Novo Usuário"
        size="md"
        asForm={true}
        handleSubmit={handleSubmit(handleAddUser)}
        isSubmitting={isSubmitting}
        confirmText="Adicionar"
        cancelText="Cancelar"
        placement="center"
      >
        <VStack gap={4} align="stretch">
          <Field.Root invalid={!!errors.name}>
            <Field.Label color="white">Nome do Usuário</Field.Label>
            <Input
              placeholder="Digite o nome do usuário"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("name")}
            />
            <Field.ErrorText>{errors.name?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.email}>
            <Field.Label color="white">Email do Usuário</Field.Label>
            <Input
              placeholder="Digite o email do usuário"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("email")}
            />
            <Field.ErrorText>{errors.email?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.email}>
            <Field.Label color="white">Senha do Usuário</Field.Label>
            <Input
              type="password"
              placeholder="Digite a senha do usuário"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("password")}
            />
            <Field.ErrorText>{errors.password?.message}</Field.ErrorText>
          </Field.Root>

          <Field.Root invalid={!!errors.email}>
            <Field.Label color="white">Confirme a Senha</Field.Label>
            <Input
              type="password"
              placeholder="Confirme a senha"
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
              color="white"
              _placeholder={{ color: "gray.400" }}
              _focus={{
                borderColor: "blue.400",
                boxShadow: "0 0 0 1px #3182ce",
              }}
              {...register("confirmPassword")}
            />
            <Field.ErrorText>{errors.confirmPassword?.message}</Field.ErrorText>
          </Field.Root>

          <FormCombobox
            label="Papel do Usuário"
            placeholder="Selecione um papel"
            options={collection.items}
            value={watchedType}
            onValueChange={(value) => setValue("role", value as any)}
            error={errors.role}
            isInvalid={!!errors.role}
          />

          <FormCombobox
            label="Gerência do Usuário"
            placeholder="Selecione uma gerência"
            options={getManagementOptions()}
            value={watch("managementId")}
            onValueChange={(value) => setValue("managementId", value as string)}
            error={errors.managementId}
            isInvalid={!!errors.managementId}
          />

          <FormCombobox
            label="Cargo do Usuário"
            placeholder="Selecione um cargo"
            options={getPositionOptions()}
            value={watch("positionId")}
            onValueChange={(value) => setValue("positionId", value as string)}
            error={errors.positionId}
            isInvalid={!!errors.positionId}
          />

          <FormCombobox
            label="Permissões do Usuário"
            placeholder="Selecione as permissões"
            options={getPermissionsOptions()}
            value={watch("permissionsIds")}
            onValueChange={(value) =>
              setValue("permissionsIds", value as string[])
            }
            error={errors.permissionsIds as any}
            isInvalid={!!errors.permissionsIds}
            multiple
          />
        </VStack>
      </BasicModal>
    </Flex>
  );
}
