"use client";
import { Accordion, Stack, Text } from "@chakra-ui/react";
import { NavLink } from "./navlink";

export default function NavLinks() {
  return (
    <Stack flex={1} alignContent="center" w="100%" p={3} gap={0}>
      <NavLink href="/management" shouldMatchExactHref={true}>
        Dashboard
      </NavLink>
      <NavLink href="/users" shouldMatchExactHref={true}>
        Usuários
      </NavLink>
      <Stack gap={0}>
        <Accordion.Root
          variant={"plain"}
          collapsible
          defaultValue={["hierarchy"]}
        >
          <Accordion.Item value={"hierarchy"}>
            <Accordion.ItemTrigger
              w={"100%"}
              px={4}
              py={0}
              cursor={"pointer"}
              justifyContent={"space-between"}
              _hover={{
                textDecoration: "none",
                background: "#000000",
                borderRadius: "5px",
              }}
            >
              <Text
                fontSize={"20px"}
                fontWeight={"light"}
                height={11}
                color={"text"}
                transition="200ms"
                alignContent={"center"}
              >
                Hierarquia
              </Text>
              <Accordion.ItemIndicator />
            </Accordion.ItemTrigger>
            <Accordion.ItemContent>
              <NavLink
                href="/categories"
                shouldMatchExactHref={true}
                p={8}
                py={0}
              >
                Categorias
              </NavLink>
              <NavLink
                href="/structure"
                shouldMatchExactHref={true}
                p={8}
                py={0}
              >
                Estrutura
              </NavLink>
            </Accordion.ItemContent>
          </Accordion.Item>
        </Accordion.Root>
      </Stack>
      {/* <NavLink
        href="/hierarchy"
        icon={<LuFilter />}
        shouldMatchExactHref={true}
      >
        Hierarquia
      </NavLink> */}
    </Stack>
  );
}
