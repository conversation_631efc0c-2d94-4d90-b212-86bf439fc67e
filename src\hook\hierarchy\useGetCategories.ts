import { api } from "@/services/api";
import { GetAllCategoriesDto } from "@/utils/types/DTO/hierarchy/categories.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllCategories(searchTerm?: string) {
  const { data } = await api.get<GetAllCategoriesDto>(`/management/hierarchy`, {
    params: {
      search: searchTerm,
    },
  });
  return data;
}

export function useGetAllCategories(searchTerm?: string) {
  return useQuery({
    queryKey: ["categories", searchTerm],
    queryFn: async () => await getAllCategories(searchTerm),
  });
}