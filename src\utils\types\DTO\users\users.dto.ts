import { UserRole } from "../../enums/user-role";
import { MetaDTO } from "../meta.dto";

export type GetAllUsersDto = {
  meta: MetaDTO;
  data: GetUserDto[];
};

export type GetUserDto = {
  secureId: string;
  name: string;
  email: string;
  role: UserRole;
  active: boolean;
  createdAt: string;
  updatedAt: string;
  adminProfile: {
    secureId: string;
    permissions: string[];
  } | null;
  respondentProfile: any; // TODO: Create respondent profile type
};