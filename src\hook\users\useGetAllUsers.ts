import { api } from "@/services/api";
import { GetAllUsersDto } from "@/utils/types/DTO/users/users.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllUsers(searchTerm?: string) {
  const { data } = await api.get<GetAllUsersDto>(`/management/user`, {
    params: {
      search: searchTerm,
    },
  });
  return data;
}

export function useGetAllUsers(searchTerm?: string) {
  return useQuery({
    queryKey: ["users", searchTerm],
    queryFn: async () => await getAllUsers(searchTerm),
  });
}