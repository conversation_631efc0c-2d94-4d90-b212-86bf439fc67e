import {
  Combobox,
  useFilter,
  useListCollection,
  Portal,
  Field,
  Badge,
  Wrap,
} from "@chakra-ui/react";
import { FieldError } from "react-hook-form";

export interface ComboboxOption {
  label: string;
  value: string;
}

export interface FormComboboxProps {
  options: ComboboxOption[];
  value?: string | string[];
  placeholder?: string;
  label?: string;
  error?: FieldError;
  isInvalid?: boolean;
  multiple?: boolean;
  onValueChange: (value: string | string[]) => void;
  onInputValueChange?: (value: string) => void;
}

export default function FormCombobox({
  options,
  value,
  placeholder = "Selecione uma opção",
  label,
  error,
  isInvalid,
  multiple = false,
  onValueChange,
  onInputValueChange,
}: FormComboboxProps) {
  const { contains } = useFilter({ sensitivity: "base" });

  const { collection, filter } = useListCollection({
    initialItems: options,
    filter: contains,
  });

  const handleInputValueChange = (inputValue: string) => {
    filter(inputValue);
    if (onInputValueChange) {
      onInputValueChange(inputValue);
    }
  };

  const handleValueChange = (details: { value: string[] }) => {
    if (multiple) {
      onValueChange(details.value);
    } else {
      const selectedValue = details.value[0];
      onValueChange(selectedValue || "");
    }
  };

  // Função para obter o label de uma opção pelo value
  const getLabelByValue = (val: string) => {
    const option = options.find((opt) => opt.value === val);
    return option?.label || val;
  };

  return (
    <Field.Root invalid={isInvalid}>
      {label && <Field.Label color="white">{label}</Field.Label>}

      {/* Mostrar badges para seleção múltipla */}
      {multiple && Array.isArray(value) && value.length > 0 && (
        <Wrap gap="2" mb={2}>
          {value.map((val) => (
            <Badge key={val} bg="blue.600" color="white">
              {getLabelByValue(val)}
            </Badge>
          ))}
        </Wrap>
      )}

      <Combobox.Root
        collection={collection}
        onInputValueChange={(e) => handleInputValueChange(e.inputValue)}
        onValueChange={handleValueChange}
        value={
          multiple
            ? Array.isArray(value)
              ? value
              : []
            : value
            ? [value as string]
            : []
        }
        multiple={multiple}
        openOnClick
      >
        <Combobox.Control>
          <Combobox.Input
            placeholder={placeholder}
            bg="gray.700"
            border="1px solid"
            borderColor="gray.600"
            color="white"
            _placeholder={{ color: "gray.400" }}
            _focus={{
              borderColor: "blue.400",
              boxShadow: "0 0 0 1px #3182ce",
            }}
          />
          <Combobox.IndicatorGroup>
            <Combobox.ClearTrigger />
            <Combobox.Trigger />
          </Combobox.IndicatorGroup>
        </Combobox.Control>
        <Portal>
          <Combobox.Positioner>
            <Combobox.Content
              zIndex={"popover"}
              bg="gray.700"
              border="1px solid"
              borderColor="gray.600"
            >
              <Combobox.Empty color="gray.400">
                Nenhum item encontrado
              </Combobox.Empty>
              {collection.items.map((item) => (
                <Combobox.Item
                  item={item}
                  key={item.value}
                  color="white"
                  _hover={{ bg: "gray.600" }}
                  _selected={{ bg: "blue.600" }}
                >
                  {item.label}
                  <Combobox.ItemIndicator />
                </Combobox.Item>
              ))}
            </Combobox.Content>
          </Combobox.Positioner>
        </Portal>
      </Combobox.Root>
      {error && <Field.ErrorText>{error.message}</Field.ErrorText>}
    </Field.Root>
  );
}
