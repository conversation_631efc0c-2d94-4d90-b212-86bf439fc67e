import { api } from "@/services/api";
import { GetAllStructuresDto } from "@/utils/types/DTO/hierarchy/structures.dto";
import { useQuery } from "@tanstack/react-query";

async function getAllStructures() {
  const { data } = await api.get<GetAllStructuresDto>(`/management/hierarchy-structure`);
  return data;
}

export function useGetAllStructures() {
  return useQuery({
    queryKey: ["structures"],
    queryFn: async () => await getAllStructures(),
  });
}